import { system } from "@minecraft/server";
/**
 * UI Sound Manager for entity-following sounds
 * Uses the UI sound category to create sounds that automatically follow entities
 */
export class UISoundManager {
    activeSounds = new Map();
    soundIntervals = new Map();
    /**
     * UI Sound definitions for each boss
     */
    static UI_SOUNDS = {
        PIGLIN_CHAMPION: {
            ambient: "ui.ptd_dbb.piglin_champion.ambient",
            spawn: "ui.ptd_dbb.piglin_champion.spawn",
            horizontal_attack: "ui.ptd_dbb.piglin_champion.horizontal_attack",
            vertical_attack: "ui.ptd_dbb.piglin_champion.vertical_attack",
            foot_stomp: "ui.ptd_dbb.piglin_champion.foot_stomp",
            spin_slam: "ui.ptd_dbb.piglin_champion.spin_slam",
            body_slam: "ui.ptd_dbb.piglin_champion.body_slam",
            charging: "ui.ptd_dbb.piglin_champion.charging",
            summoning_chant: "ui.ptd_dbb.piglin_champion.summoning_chant",
            healing: "ui.ptd_dbb.piglin_champion.healing",
            stunned_standing: "ui.ptd_dbb.piglin_champion.stunned_standing"
        },
        NECROMANCER: {
            ambient: "ui.ptd_dbb.necromancer.ambient",
            spawn: "ui.ptd_dbb.necromancer.spawn",
            cataclysm: "ui.ptd_dbb.necromancer.cataclysm",
            soul_drain: "ui.ptd_dbb.necromancer.soul_drain",
            phantom_phase: "ui.ptd_dbb.necromancer.phantom_phase",
            arcane_blast: "ui.ptd_dbb.necromancer.arcane_blast"
        },
        GRIMHOWL: {
            ambient: "ui.ptd_dbb.grimhowl.ambient",
            roar: "ui.ptd_dbb.grimhowl.roar",
            sword_swipe: "ui.ptd_dbb.grimhowl.sword_swipe",
            claw_attack: "ui.ptd_dbb.grimhowl.claw_attack"
        }
    };
    /**
     * Play a UI sound that follows the entity
     * @param entity - The entity to attach the sound to
     * @param soundId - The UI sound identifier
     * @param radius - The radius for players to hear the sound (default: 32)
     * @param duration - How long to play the sound in ticks (default: 20)
     */
    playUISound(entity, soundId, radius = 32, duration = 20) {
        if (!entity.isValid())
            return;
        try {
            // Play the UI sound at entity location
            entity.runCommand(`playsound ${soundId} @a[r=${radius}] ~ ~ ~ 1.0 1.0`);
            // Store the sound for tracking
            this.activeSounds.set(`${entity.id}_${soundId}`, Date.now());
        }
        catch (error) {
            console.warn(`Failed to play UI sound ${soundId} for entity ${entity.id}:`, error);
        }
    }
    /**
     * Play a looping UI sound that follows the entity
     * @param entity - The entity to attach the sound to
     * @param soundId - The UI sound identifier
     * @param interval - How often to replay the sound in ticks (default: 40)
     * @param radius - The radius for players to hear the sound (default: 32)
     */
    playLoopingUISound(entity, soundId, interval = 40, radius = 32) {
        if (!entity.isValid())
            return;
        const key = `${entity.id}_${soundId}`;
        // Stop any existing loop for this entity/sound combination
        this.stopLoopingUISound(entity.id, soundId);
        // Start the looping sound
        const intervalId = system.runInterval(() => {
            if (!entity.isValid()) {
                this.stopLoopingUISound(entity.id, soundId);
                return;
            }
            try {
                entity.runCommand(`playsound ${soundId} @a[r=${radius}] ~ ~ ~ 1.0 1.0`);
            }
            catch (error) {
                console.warn(`Failed to play looping UI sound ${soundId}:`, error);
                this.stopLoopingUISound(entity.id, soundId);
            }
        }, interval);
        this.soundIntervals.set(key, intervalId);
    }
    /**
     * Stop a looping UI sound for an entity
     * @param entityId - The entity ID
     * @param soundId - The UI sound identifier
     */
    stopLoopingUISound(entityId, soundId) {
        const key = `${entityId}_${soundId}`;
        const intervalId = this.soundIntervals.get(key);
        if (intervalId !== undefined) {
            system.clearRun(intervalId);
            this.soundIntervals.delete(key);
        }
    }
    /**
     * Stop all looping sounds for an entity
     * @param entityId - The entity ID
     */
    stopAllEntitySounds(entityId) {
        const keysToRemove = [];
        for (const [key, intervalId] of this.soundIntervals.entries()) {
            if (key.startsWith(entityId + "_")) {
                system.clearRun(intervalId);
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => this.soundIntervals.delete(key));
    }
    /**
     * Play a UI sound for a specific boss attack
     * @param entity - The boss entity
     * @param bossType - The type of boss (PIGLIN_CHAMPION, NECROMANCER, GRIMHOWL)
     * @param attackType - The attack type
     * @param radius - The radius for players to hear the sound
     */
    playBossAttackSound(entity, bossType, attackType, radius = 32) {
        const bossUISounds = UISoundManager.UI_SOUNDS[bossType];
        const soundId = bossUISounds[attackType];
        if (soundId) {
            this.playUISound(entity, soundId, radius);
        }
        else {
            console.warn(`No UI sound found for ${bossType} attack: ${attackType}`);
        }
    }
    /**
     * Start ambient UI sounds for a boss
     * @param entity - The boss entity
     * @param bossType - The type of boss
     * @param interval - How often to play ambient sound in ticks
     */
    startBossAmbientSound(entity, bossType, interval = 100) {
        const bossUISounds = UISoundManager.UI_SOUNDS[bossType];
        if (bossUISounds.ambient) {
            this.playLoopingUISound(entity, bossUISounds.ambient, interval);
        }
    }
    /**
     * Clean up all sounds (call this when shutting down)
     */
    cleanup() {
        for (const intervalId of this.soundIntervals.values()) {
            system.clearRun(intervalId);
        }
        this.soundIntervals.clear();
        this.activeSounds.clear();
    }
}
// Global instance
export const uiSoundManager = new UISoundManager();
